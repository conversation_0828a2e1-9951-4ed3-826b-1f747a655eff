{"dependencies": {"@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "echarts": "^5.6.0", "vant": "^2.13.6"}, "devDependencies": {"autoprefixer": "^9.8.6", "cross-env": "^7.0.3", "css-loader": "^4.2.2", "postcss": "^7.0.39", "postcss-loader": "^4.1.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vconsole": "^3.15.1"}, "scripts": {"dev": "cross-env NODE_ENV=development uni-app serve", "build": "cross-env NODE_ENV=production uni-app build && node scripts/build-cleanup.js", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 uni-app build && node scripts/build-cleanup.js ./unpackage/dist/h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus uni-app build && node scripts/build-cleanup.js ./unpackage/dist/app-plus", "cleanup": "node scripts/build-cleanup.js", "build:clean": "npm run build:h5 && npm run cleanup"}}