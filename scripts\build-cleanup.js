const fs = require('fs');
const path = require('path');

/**
 * 构建后清理脚本
 * 用于清理构建输出中的重复文件、.map文件和以点开头的文件
 */
class BuildCleanup {
  constructor(outputDir) {
    // 规范化并验证输出目录路径
    this.outputDir = path.resolve(outputDir);
    this.basePath = path.resolve(process.cwd());

    // 确保输出目录在项目根目录内
    if (!this.outputDir.startsWith(this.basePath)) {
      throw new Error('输出目录必须在项目根目录内');
    }

    this.processedFiles = new Set();
    this.duplicateFiles = [];
    this.mapFiles = [];
    this.hiddenFiles = [];
  }

  /**
   * 验证文件路径是否安全
   */
  validatePath(filePath) {
    const normalizedPath = path.resolve(filePath);
    return normalizedPath.startsWith(this.basePath);
  }

  /**
   * 递归扫描目录
   */
  scanDirectory(dir) {
    // 验证目录路径安全性
    const normalizedDir = path.resolve(dir);
    if (!this.validatePath(normalizedDir)) {
      console.error(`不安全的目录路径: ${dir}`);
      return;
    }

    if (!fs.existsSync(normalizedDir)) {
      console.log(`目录不存在: ${normalizedDir}`);
      return;
    }

    const files = fs.readdirSync(normalizedDir);

    files.forEach(file => {
      const filePath = path.resolve(normalizedDir, file);

      // 验证文件路径安全性
      if (!this.validatePath(filePath)) {
        console.error(`不安全的文件路径: ${filePath}`);
        return;
      }

      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        this.scanDirectory(filePath);
      } else {
        this.processFile(filePath, file);
      }
    });
  }

  /**
   * 处理单个文件
   */
  processFile(filePath, fileName) {
    // 检查是否为.map文件
    if (fileName.endsWith('.map')) {
      this.mapFiles.push(filePath);
      return;
    }

    // 检查是否为以点开头的文件
    if (fileName.startsWith('.')) {
      this.hiddenFiles.push(filePath);
      return;
    }

    // 检查重复文件（基于文件内容hash）
    try {
      // 再次验证文件路径安全性
      if (!this.validatePath(filePath)) {
        console.error(`不安全的文件路径: ${filePath}`);
        return;
      }

      const content = fs.readFileSync(filePath);
      const hash = require('crypto').createHash('md5').update(content).digest('hex');
      
      if (this.processedFiles.has(hash)) {
        this.duplicateFiles.push(filePath);
      } else {
        this.processedFiles.add(hash);
      }
    } catch (error) {
      console.warn(`无法读取文件: ${filePath}`, error.message);
    }
  }

  /**
   * 删除文件
   */
  deleteFiles(files, type) {
    if (files.length === 0) {
      console.log(`没有找到${type}文件`);
      return;
    }

    console.log(`\n发现 ${files.length} 个${type}文件:`);
    files.forEach(file => {
      console.log(`  - ${file}`);
      try {
        // 验证文件路径安全性
        if (!this.validatePath(file)) {
          console.error(`    ✗ 不安全的文件路径: ${file}`);
          return;
        }

        fs.unlinkSync(file);
        console.log(`    ✓ 已删除`);
      } catch (error) {
        console.error(`    ✗ 删除失败: ${error.message}`);
      }
    });
  }

  /**
   * 重命名文件，确保有codepass前缀
   */
  ensureCodepassPrefix(dir) {
    // 验证目录路径安全性
    const normalizedDir = path.resolve(dir);
    if (!this.validatePath(normalizedDir)) {
      console.error(`不安全的目录路径: ${dir}`);
      return;
    }

    if (!fs.existsSync(normalizedDir)) {
      return;
    }

    const files = fs.readdirSync(normalizedDir);

    files.forEach(file => {
      const filePath = path.resolve(normalizedDir, file);

      // 验证文件路径安全性
      if (!this.validatePath(filePath)) {
        console.error(`不安全的文件路径: ${filePath}`);
        return;
      }

      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        this.ensureCodepassPrefix(filePath);
      } else {
        // 检查文件是否已经有codepass前缀
        if (!file.startsWith('codepass-') && !file.startsWith('.') && !file.endsWith('.html')) {
          const newFileName = `codepass-${file}`;
          const newFilePath = path.resolve(normalizedDir, newFileName);

          // 验证新文件路径安全性
          if (!this.validatePath(newFilePath)) {
            console.error(`不安全的新文件路径: ${newFilePath}`);
            return;
          }

          try {
            fs.renameSync(filePath, newFilePath);
            console.log(`重命名: ${file} -> ${newFileName}`);
          } catch (error) {
            console.warn(`重命名失败: ${file}`, error.message);
          }
        }
      }
    });
  }

  /**
   * 执行清理
   */
  cleanup() {
    console.log(`开始清理构建输出目录: ${this.outputDir}`);
    
    // 扫描所有文件
    this.scanDirectory(this.outputDir);
    
    // 删除.map文件
    this.deleteFiles(this.mapFiles, '.map');
    
    // 删除以点开头的文件
    this.deleteFiles(this.hiddenFiles, '隐藏');
    
    // 删除重复文件
    this.deleteFiles(this.duplicateFiles, '重复');
    
    // 确保所有文件都有codepass前缀
    console.log('\n检查文件前缀...');
    this.ensureCodepassPrefix(this.outputDir);
    
    console.log('\n清理完成!');
  }

  /**
   * 生成清理报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      outputDir: this.outputDir,
      summary: {
        mapFiles: this.mapFiles.length,
        hiddenFiles: this.hiddenFiles.length,
        duplicateFiles: this.duplicateFiles.length,
        totalProcessed: this.processedFiles.size
      },
      details: {
        mapFiles: this.mapFiles,
        hiddenFiles: this.hiddenFiles,
        duplicateFiles: this.duplicateFiles
      }
    };

    const reportPath = path.resolve(this.outputDir, 'cleanup-report.json');

    // 验证报告文件路径安全性
    if (!this.validatePath(reportPath)) {
      console.error(`不安全的报告文件路径: ${reportPath}`);
      return report;
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`清理报告已生成: ${reportPath}`);
    
    return report;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const outputDir = process.argv[2] || './unpackage/dist/h5';
  const cleanup = new BuildCleanup(outputDir);
  cleanup.cleanup();
  cleanup.generateReport();
}

module.exports = BuildCleanup;
