# 本地开发环境SSL证书配置指南

## 概述

本项目的开发服务器配置为使用HTTPS（在`manifest.json`中设置了`"https": true`），因此需要SSL证书才能正常启动开发服务器。

## 快速设置

### 方法一：使用自动生成脚本（推荐）

1. 安装依赖（如果还没有安装）：
```bash
npm install readline-sync --save-dev
```

2. 运行SSL证书生成脚本：
```bash
node scripts/generate-ssl-cert.js
```

3. 按照提示完成证书生成

### 方法二：手动生成证书

如果您已安装OpenSSL，可以手动生成证书：

```bash
# 创建ssl目录
mkdir ssl

# 生成私钥
openssl genrsa -out ssl/private.key 2048

# 生成自签名证书
openssl req -new -x509 -key ssl/private.key -out ssl/certificate.crt -days 365 -subj "/C=CN/ST=Local/L=Local/O=Development/OU=Development/CN=localhost"
```

### 方法三：使用mkcert（推荐用于团队开发）

mkcert是一个简单的工具，可以生成本地信任的开发证书：

1. 安装mkcert：
   - Windows: `choco install mkcert` 或下载二进制文件
   - macOS: `brew install mkcert`
   - Linux: 参考[mkcert官方文档](https://github.com/FiloSottile/mkcert)

2. 安装本地CA：
```bash
mkcert -install
```

3. 生成证书：
```bash
mkcert -key-file ssl/private.key -cert-file ssl/certificate.crt localhost 127.0.0.1 ::1
```

### 方法四：禁用HTTPS（不推荐）

如果您不需要HTTPS，可以修改`manifest.json`：

```json
"h5": {
    "devServer": {
        "port": 8080,
        "https": false,  // 改为false
        "disableHostCheck": true,
        // ...其他配置
    }
}
```

## 文件结构

生成的SSL文件应该位于：
```
ssl/
├── private.key     # 私钥文件（已在.gitignore中忽略）
└── certificate.crt # 证书文件
```

## 浏览器警告处理

使用自签名证书时，浏览器会显示安全警告：

1. **Chrome/Edge**: 点击"高级" → "继续前往localhost（不安全）"
2. **Firefox**: 点击"高级" → "接受风险并继续"
3. **Safari**: 点击"显示详细信息" → "访问此网站"

## 安全注意事项

⚠️ **重要提醒**：

1. **仅用于开发环境**：这些证书仅用于本地开发，不要在生产环境使用
2. **私钥保护**：私钥文件已被添加到`.gitignore`中，请勿提交到代码库
3. **定期更新**：建议每年更新一次证书
4. **团队协作**：每个开发者都应该生成自己的证书，不要共享私钥

## 故障排除

### 问题1：找不到openssl命令

**解决方案**：
- Windows: 下载并安装 [OpenSSL for Windows](https://slproweb.com/products/Win32OpenSSL.html)
- macOS: `brew install openssl`
- Linux: `sudo apt-get install openssl` 或 `sudo yum install openssl`

### 问题2：证书过期

**解决方案**：
重新运行生成脚本或手动生成新证书

### 问题3：浏览器仍然显示不安全

**解决方案**：
1. 清除浏览器缓存
2. 确保证书文件路径正确
3. 检查证书是否过期
4. 考虑使用mkcert生成受信任的证书

## 生产环境部署

在生产环境中，请使用正式的SSL证书：
- Let's Encrypt（免费）
- 商业SSL证书提供商
- 云服务商提供的SSL证书服务

## 相关文件

- `scripts/generate-ssl-cert.js` - SSL证书生成脚本
- `manifest.json` - 开发服务器配置
- `.gitignore` - 包含SSL私钥忽略规则
