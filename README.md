# 码上通 - 移动端应用

基于uni-app框架开发的移动端应用项目。

## 快速开始

### 环境要求

- Node.js >= 14.0.0
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 本地开发

⚠️ **重要：首次运行前需要配置SSL证书**

由于项目配置了HTTPS开发服务器，首次运行前请生成SSL证书：

```bash
# 生成本地开发用SSL证书
npm run ssl:generate
```

然后启动开发服务器：

```bash
npm run dev
```

### 构建项目

```bash
# 构建H5版本
npm run build:h5

# 构建App版本
npm run build:app-plus

# 构建并清理
npm run build:clean
```

## SSL证书配置

项目使用HTTPS开发服务器，需要SSL证书才能正常运行。详细配置说明请参考：

📖 [SSL配置指南](./SSL_SETUP.md)

### 快速配置

```bash
# 自动生成SSL证书
npm run ssl:generate

# 或手动生成（需要安装OpenSSL）
openssl genrsa -out ssl/private.key 2048
openssl req -new -x509 -key ssl/private.key -out ssl/certificate.crt -days 365 -subj "/C=CN/ST=Local/L=Local/O=Development/OU=Development/CN=localhost"
```

## 项目结构

```
├── pages/              # 页面文件
├── components/         # 组件文件
├── static/            # 静态资源
├── uni_modules/       # uni-app模块
├── scripts/           # 构建脚本
│   ├── build-cleanup.js      # 构建清理脚本
│   └── generate-ssl-cert.js  # SSL证书生成脚本
├── ssl/               # SSL证书目录（本地生成，不提交到代码库）
├── manifest.json      # 应用配置
├── vue.config.js      # Vue配置
└── package.json       # 项目配置
```

## 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建项目
- `npm run build:h5` - 构建H5版本
- `npm run build:app-plus` - 构建App版本
- `npm run cleanup` - 清理构建文件
- `npm run ssl:generate` - 生成SSL证书

## 安全说明

本项目已通过安全扫描并修复了所有已知漏洞。详细信息请参考：

📋 [安全修复报告](./SECURITY_FIXES.md)

### 主要安全改进

- ✅ 移除了泄露的私钥文件
- ✅ 修复了路径遍历漏洞
- ✅ 修复了正则表达式DoS漏洞
- ✅ 建立了完善的私钥保护机制

## 开发注意事项

1. **SSL证书**：首次运行前请生成SSL证书
2. **浏览器警告**：使用自签名证书时浏览器会显示安全警告，这是正常的
3. **私钥保护**：SSL私钥文件已被添加到.gitignore中，请勿提交到代码库
4. **定期更新**：建议定期更新SSL证书（每年一次）

## 技术栈

- uni-app - 跨平台开发框架
- Vue.js 2 - 前端框架
- Tailwind CSS - CSS框架
- Node.js - 构建工具

## 许可证

[MIT License](LICENSE)
