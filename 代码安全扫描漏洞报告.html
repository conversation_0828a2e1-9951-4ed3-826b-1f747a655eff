<!DOCTYPE html>
<!-- saved from url=(0014)about:internet -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码安全扫描漏洞报告</title>
    <!-- <script type="text/javascript" src="./clipboard.min.js"></script> -->
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            width: 90%;
            margin: auto;
            overflow: hidden;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header, .summary, .details {
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
        }
        .header .info {
            display: flex;
            justify-content: space-between;
            flex-wrap: nowrap;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }
        .summary .tile {
            background-color: #f2f2f2;
            padding: 20px;
            margin: 5px;
            flex: 1 1 calc(16.66% - 10px);
            text-align: center;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
        }
        .tile h2 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .tile p {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }
        .tile.severity-high {
            background-color: #f8d7da;
        }
        .tile.severity-medium {
            background-color: #fff3cd;
        }
        .tile.severity-low {
            background-color: #d1ecf1;
        }
        .tile.severity-info {
            background-color: #cce5ff;
        }
        .tile.severity-unknown {
            background-color: #e2e3e5;
        }
        .details table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        .details table th, .details table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            word-wrap: break-word;
        }
        .details table th {
            background-color: #f2f2f2;
        }
        @media (max-width: 1200px) {
            .summary .tile {
                flex: 1 1 calc(33.33% - 10px);
            }
        }
        @media (max-width: 768px) {
            .header .info {
                flex-direction: column;
                align-items: flex-start;
            }
            .summary .tile {
                flex: 1 1 calc(50% - 10px);
            }
        }
        @media (max-width: 480px) {
            .summary .tile {
                flex: 1 1 100%;
            }
        }
    </style>
    <style>
        p {
            margin-top: 0;
            margin-bottom: 1rem;
        }
        a {
            color: #1f75cb;
            text-decoration: none;
            background-color: transparent;
            word-break: break-all;
        }
        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            margin-bottom: 10px;
        }
        .icon svg {
            width: 12px;
            height: 12px;
            margin-right: 0.5rem;
            fill: currentColor;
            vertical-align: -1px;
        }
        .vuln-title-link {
            cursor: pointer;
        }
        tr:hover .vuln-title-link {
            text-decoration: underline;
        }
        .vuln-title-link:hover {
            color: #1f75cb;
        }
        .vuln-position {
            font-size: 12px;
            color: #535158;
        }
        .scan-vender {
            color: #a4a3a8;
        }
        .modal {
            font-family: var(--default-regular-font, "GitLab Sans"), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #333238;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }
        .modal.open {
            display: block;
        }
        .modal.open .modal-content {
            animation: fade .3s;
        }
        .modal-content {
            background-color: #fff;
            color: #333238;
            margin: 15% auto;
            padding: 20px 24px;
            border: 1px solid #888;
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            width: 80%;
            max-width: 1000px;
        }
        .modal-header {
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 10px;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
        .modal-title {
            font-weight: 600;
            line-height: 1.2;
        }
        .modal-body p {
            word-wrap: break-word;
            word-break: break-all;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .detected-icon {
            background-color: #f5d9a8;
            color: #8f4700;
            border-radius: 10rem;
            padding: 0.25rem 0.5rem;
            line-height: 1rem;
            font-size: 0.75rem;
        }
        .modal-body section {
            padding-bottom: 1rem;
            margin-bottom: 10px;
        }
        .description-section ul {
            margin-bottom: 1rem;
        }
        .links-section li {
            list-style: disc;
        }
        .modal-body section .head {
            display: block;
            font-size: 16px;
            font-weight: 550;
            margin-bottom: 8px;
        }
        .solution-section {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 1rem;
            border: 1px solid #dcdcde;
            border-radius: 4px;
            margin: 1rem 0;
        }
        @keyframes fade {
            0% {
                opacity: 0;
                -webkit-transform: scale3d(0, 0, 1);
            }
            100% {
                opacity: 1;
                -webkit-transform: scale3d(1, 1, 1);
            }
        }
        .btn {
            border: 1px solid #888;
            background-color: #fff;
            padding: 6px 18px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            border-color: #666;
        }
        .close {
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 24px;
        }

        .gl-relative {
            position: relative;
        }
        .markdown-code-block pre {
            padding: 12px;
        }
        .code {
            padding: 0;
            border-radius: 0 0 4px 4px;
        }
        .highlight {
            text-shadow: none;
        }
        pre {
            position: relative;
            font-family: var(--default-mono-font, "GitLab Mono"), "JetBrains Mono", "Menlo", "DejaVu Sans Mono", "Liberation Mono", "Consolas", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace;
            display: block;
            padding: 8px 12px;
            margin: 0 0 8px;
            font-size: 0.875rem;
            word-break: break-all;
            word-wrap: break-word;
            color: #333238;
            background-color: #fbfafd;
            border: 1px solid #dcdcde;
            border-radius: 2px;
            overflow: auto;
        }
        pre code {
            white-space: pre-wrap;
            font-size: inherit;
            color: inherit;
            word-break: normal;
        }
        copy-code {
            position: absolute;
            transition: all 0.2s ease;
            opacity: 0;
            top: 7px;
            right: 12px;
        }
        .gl-button.gl-button.btn-icon {
            padding: 0.5rem;
            line-height: 1rem;
        }
        .gl-button.gl-button.btn-default:hover {
            box-shadow: inset 0 0 0 2px #89888d, 0 2px 2px 0 rgba(0, 0, 0, 0.08);
            background: #ececef;
        }
    </style>
</head>
<body>
<div class="container">
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" id="severity-critical"><path fill-rule="evenodd" d="M6.706.185l4.088 2.31c.437.246.706.702.706 1.195v4.62c0 .493-.269.949-.706 1.195l-4.088 2.31a1.438 1.438 0 0 1-1.412 0l-4.088-2.31A1.376 1.376 0 0 1 .5 8.31V3.69c0-.493.269-.949.706-1.195L5.294.185a1.438 1.438 0 0 1 1.412 0z"></path></symbol>
        <symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" id="severity-high"><path fill-rule="evenodd" d="M6.713.295l4.992 4.992a1.008 1.008 0 0 1 0 1.426l-4.992 4.992a1.008 1.008 0 0 1-1.426 0L.295 6.713a1.008 1.008 0 0 1 0-1.426L5.287.295a1.008 1.008 0 0 1 1.426 0z"></path></symbol>
        <symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" id="severity-info"><path fill-rule="evenodd" d="M12 6A6 6 0 1 1 0 6a6 6 0 0 1 12 0zM6.75 3.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0zM6 5.25a.75.75 0 0 0-.75.75v2.25a.75.75 0 1 0 1.5 0V6A.75.75 0 0 0 6 5.25z"></path></symbol>
        <symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" id="severity-low"><circle cx="6" cy="6" r="6" fill-rule="evenodd"></circle></symbol>
        <symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" id="severity-medium"><path fill-rule="evenodd" d="M5.04 10.454L.24 3.182c-.398-.603-.29-1.457.24-1.91C.688 1.097.94 1 1.2 1h9.6c.663 0 1.2.61 1.2 1.364 0 .295-.084.582-.24.818l-4.8 7.272c-.398.603-1.15.725-1.68.273a1.295 1.295 0 0 1-.24-.273z"></path></symbol>
        <symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" id="severity-unknown"><path fill-rule="evenodd" d="M6 12A6 6 0 1 1 6 0a6 6 0 0 1 0 12zM3.324 4.643c0-.313.107-.631.32-.953.214-.322.526-.589.935-.8.41-.212.887-.317 1.433-.317.508 0 .956.088 1.344.265.389.176.689.417.9.72.212.304.318.635.318.991 0 .281-.06.527-.18.738-.121.212-.265.394-.431.548-.167.153-.465.412-.895.775a3.549 3.549 0 0 0-.287.27 1.1 1.1 0 0 0-.16.213c-.289.667-1.543.592-1.302-.342a1.82 1.82 0 0 1 .363-.535c.15-.153.353-.336.609-.547.224-.185.385-.325.485-.419.1-.094.184-.199.252-.314a.727.727 0 0 0 .103-.377.854.854 0 0 0-.313-.669c-.208-.181-.477-.272-.806-.272-.385 0-.668.092-.85.275-.182.183-.336.453-.462.81-.12.373-.345.56-.677.56a.686.686 0 0 1-.496-.196c-.135-.13-.203-.272-.203-.424zM6 9.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5z"></path></symbol>
    </svg>
    <!-- 扫描基本信息 -->
    <div class="header">
        <h1>代码安全扫描漏洞报告</h1>
        <div class="info">
            <p>扫描对象：<span id="scan-target">sc-igw-app_1001249【main】</span></p>
            <p>扫描时间：<span id="scan-time">2025-08-08 12:57:01</span></p>
        </div>
    </div>
    <!-- 漏洞分级统计 -->
    <div class="summary">
        <div class="tile severity-high">
            <h2>严重</h2>
            <p id="critical-count">1</p>
        </div>
        <div class="tile severity-high">
            <h2>高危</h2>
            <p id="high-count">0</p>
        </div>
        <div class="tile severity-medium">
            <h2>中危</h2>
            <p id="medium-count">10</p>
        </div>
        <div class="tile severity-low">
            <h2>低危</h2>
            <p id="low-count">0</p>
        </div>
        <div class="tile severity-info">
            <h2>信息</h2>
            <p id="info-count">0</p>
        </div>
        <div class="tile severity-unknown">
            <h2>未知</h2>
            <p id="unknown-count">0</p>
        </div>
    </div>
    <!-- 漏洞详情 -->
    <div class="details">
        <h2>漏洞详情</h2>
        <table>
            <thead>
            <tr><th>检测到</th><th>严重级别</th><th>描述</th><th>标识</th><th>工具</th></tr>
            </thead>
            <tbody id="tbody">
            <tr data-scanner="Gitleaks" data-severity="critical" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/ssl/private.key:1" data-description="&lt;p data-sourcepos=&quot;1:1-1:17&quot; dir=&quot;auto&quot;&gt;PKCS8 private key&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>严重</td>
                <td>
                    <span class="vuln-title-link">PKCS8 private key</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/ssl/private.key</div>
                </td>
                <td>Gitleaks rule ID PKCS8 private key</td>
                <td>
                    <div class="scan-type">Secret 检测</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/scripts/build-cleanup.js:58" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/scripts/build-cleanup.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/scripts/build-cleanup.js:171" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/scripts/build-cleanup.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/uni_modules/uni-forms/components/uni-forms/validate.js:28" data-description="&lt;p data-sourcepos=&quot;1:1-5:39&quot; dir=&quot;auto&quot;&gt;The &lt;code&gt;RegExp&lt;/code&gt; constructor was called with a non-literal value. If an adversary were able to&amp;#x000A;supply a malicious regex, they could cause a Regular Expression Denial of Service (ReDoS)&amp;#x000A;against the application. In Node applications, this could cause the entire application to no&amp;#x000A;longer&amp;#x000A;be responsive to other users&#39; requests.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;7:1-11:51&quot; dir=&quot;auto&quot;&gt;To remediate this issue, never allow user-supplied regular expressions. Instead, the regular&amp;#x000A;expression should be&amp;#x000A;hardcoded. If this is not possible, consider using an alternative regular expression engine&amp;#x000A;such as &lt;a href=&quot;https://www.npmjs.com/package/re2&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;node-re2&lt;/a&gt;. RE2 is a safe alternative that does not&amp;#x000A;support backtracking, which is what leads to ReDoS.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;13:1-14:31&quot; dir=&quot;auto&quot;&gt;Example using re2 which does not support backtracking (Note: it is still recommended to&amp;#x000A;never use user-supplied input):&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;15:1-28:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Import the re2 module&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const RE2 = require(&#39;re2&#39;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;function match(userSuppliedRegex, userInput) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // Create a RE2 object with the user supplied regex, this is relatively safe&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // due to RE2 not supporting backtracking which can be abused to cause long running&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // queries&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    var re = new RE2(userSuppliedRegex);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // Execute the regular expression against some userInput&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    var result = re.exec(userInput);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // Work with the result&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;30:1-30:51&quot; dir=&quot;auto&quot;&gt;For more information on Regular Expression DoS see:&lt;/p&gt;&amp;#x000A;&lt;ul data-sourcepos=&quot;31:1-31:86&quot; dir=&quot;auto&quot;&gt;&amp;#x000A;&lt;li data-sourcepos=&quot;31:1-31:86&quot;&gt;&lt;a href=&quot;https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS&lt;/a&gt;&lt;/li&gt;&amp;#x000A;&lt;/ul&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Regular expression with non-literal value</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/uni_modules/uni-forms/components/uni-forms/validate.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-regexp">eslint.detect-non-literal-regexp</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/185.html">CWE-185</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-regexp</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/vue.config.js:129" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/vue.config.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/scripts/build-cleanup.js:84" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/scripts/build-cleanup.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/vue.config.js:138" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/vue.config.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/scripts/build-cleanup.js:21" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/scripts/build-cleanup.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/vue.config.js:132" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/vue.config.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/scripts/build-cleanup.js:100" data-description="&lt;p data-sourcepos=&quot;1:1-3:79&quot; dir=&quot;auto&quot;&gt;The application dynamically constructs file or path information. If the path&amp;#x000A;information comes from user-supplied input, it could be abused to read sensitive files,&amp;#x000A;access other users&#39; data, or aid in exploitation to gain further system access.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;5:1-9:43&quot; dir=&quot;auto&quot;&gt;User input should never be used in constructing paths or files for interacting&amp;#x000A;with the filesystem. This includes filenames supplied by user uploads or downloads.&amp;#x000A;If possible, consider hashing user input or using unique values and&amp;#x000A;use &lt;code&gt;path.normalize&lt;/code&gt; to resolve and validate the path information&amp;#x000A;prior to processing any file functionality.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;11:1-11:66&quot; dir=&quot;auto&quot;&gt;Example using &lt;code&gt;path.normalize&lt;/code&gt; and not allowing direct user input:&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;12:1-29:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// User input, saved only as a reference&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// id is a randomly generated UUID to be used as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const userData = {userFilename: userSuppliedFilename, id: crypto.randomUUID()};&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Restrict all file processing to this directory only&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const basePath = &#39;/app/restricted/&#39;;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Create the full path, but only use our random generated id as the filename&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const joinedPath = path.join(basePath, userData.id);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Normalize path, removing any &#39;..&#39;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const fullPath = path.normalize(joinedPath);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Verify the fullPath is contained within our basePath&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;if (!fullPath.startsWith(basePath)) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC13&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    console.log(&quot;Invalid path specified!&quot;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC14&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC15&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Process / work with file&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC16&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// ...&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;31:1-32:54&quot; dir=&quot;auto&quot;&gt;For more information on path traversal issues see OWASP:&amp;#x000A;&lt;a href=&quot;https://owasp.org/www-community/attacks/Path_Traversal&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Path_Traversal&lt;/a&gt;&lt;/p&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Improper limitation of a pathname to a restricted directory ('Path Traversal')</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/scripts/build-cleanup.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-fs-filename">eslint.detect-non-literal-fs-filename</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/22.html">CWE-22</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-fs-filename</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr><tr data-scanner="Semgrep" data-severity="medium" data-evidence="" data-evidence-value="" data-solution="" data-links="" data-image="" data-namespace="" data-position="src/codepass-app-ruoyi-2/uni_modules/uni-forms/components/uni-forms/validate.js:99" data-description="&lt;p data-sourcepos=&quot;1:1-5:39&quot; dir=&quot;auto&quot;&gt;The &lt;code&gt;RegExp&lt;/code&gt; constructor was called with a non-literal value. If an adversary were able to&amp;#x000A;supply a malicious regex, they could cause a Regular Expression Denial of Service (ReDoS)&amp;#x000A;against the application. In Node applications, this could cause the entire application to no&amp;#x000A;longer&amp;#x000A;be responsive to other users&#39; requests.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;7:1-11:51&quot; dir=&quot;auto&quot;&gt;To remediate this issue, never allow user-supplied regular expressions. Instead, the regular&amp;#x000A;expression should be&amp;#x000A;hardcoded. If this is not possible, consider using an alternative regular expression engine&amp;#x000A;such as &lt;a href=&quot;https://www.npmjs.com/package/re2&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;node-re2&lt;/a&gt;. RE2 is a safe alternative that does not&amp;#x000A;support backtracking, which is what leads to ReDoS.&lt;/p&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;13:1-14:31&quot; dir=&quot;auto&quot;&gt;Example using re2 which does not support backtracking (Note: it is still recommended to&amp;#x000A;never use user-supplied input):&lt;/p&gt;&amp;#x000A;&lt;div class=&quot;gl-relative markdown-code-block js-markdown-code&quot;&gt;&amp;#x000A;&lt;pre data-sourcepos=&quot;15:1-28:3&quot; class=&quot;code highlight js-syntax-highlight language-plaintext&quot; lang=&quot;plaintext&quot; v-pre=&quot;true&quot;&gt;&lt;code&gt;&lt;span id=&quot;LC1&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;// Import the re2 module&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC2&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;const RE2 = require(&#39;re2&#39;);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC3&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC4&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;function match(userSuppliedRegex, userInput) {&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC5&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // Create a RE2 object with the user supplied regex, this is relatively safe&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC6&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // due to RE2 not supporting backtracking which can be abused to cause long running&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC7&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // queries&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC8&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    var re = new RE2(userSuppliedRegex);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC9&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // Execute the regular expression against some userInput&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC10&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    var result = re.exec(userInput);&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC11&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;    // Work with the result&lt;/span&gt;&amp;#x000A;&lt;span id=&quot;LC12&quot; class=&quot;line&quot; lang=&quot;plaintext&quot;&gt;}&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&amp;#x000A;&lt;copy-code&gt;&lt;/copy-code&gt;&amp;#x000A;&lt;/div&gt;&amp;#x000A;&lt;p data-sourcepos=&quot;30:1-30:51&quot; dir=&quot;auto&quot;&gt;For more information on Regular Expression DoS see:&lt;/p&gt;&amp;#x000A;&lt;ul data-sourcepos=&quot;31:1-31:86&quot; dir=&quot;auto&quot;&gt;&amp;#x000A;&lt;li data-sourcepos=&quot;31:1-31:86&quot;&gt;&lt;a href=&quot;https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS&quot; rel=&quot;nofollow noreferrer noopener&quot; target=&quot;_blank&quot;&gt;https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS&lt;/a&gt;&lt;/li&gt;&amp;#x000A;&lt;/ul&gt;">
                <td>2025-08-08 11:56:40</td>
                <td>中</td>
                <td>
                    <span class="vuln-title-link">Regular expression with non-literal value</span>
                    <div class="vuln-position">src/codepass-app-ruoyi-2/uni_modules/uni-forms/components/uni-forms/validate.js</div>
                </td>
                <td><a target="_blank" ,="" href="https://semgrep.dev/r/gitlab.eslint.detect-non-literal-regexp">eslint.detect-non-literal-regexp</a><br><a target="_blank" ,="" href="https://cwe.mitre.org/data/definitions/185.html">CWE-185</a><br>A03:2021 - Injection<br>A1:2017 - Injection<br>ESLint rule ID/detect-non-literal-regexp</td>
                <td>
                    <div class="scan-type">SAST</div>
                    <div class="scan-vender">GitLab</div>
                </td>
            </tr>
            <!-- 其他漏洞详情 -->
            </tbody>
        </table>
    </div>
    <div class="modal" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title" id="myModalLabel">漏洞详情</span>
                <button type="button" onclick="closeDialog()" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
            </div>
            <div class="modal-body">
                <!-- 显示漏洞详情 -->
                <div id="myModalBody"></div>
            </div>
            <div class="modal-footer"></div>
        </div>
    </div>
</div>
<script>
    const severityOptions = {
        critical: { icon: 'severity-critical', color: '#8d1300' },
        high: { icon: 'severity-high', color: '#c91c00' },
        medium: { icon: 'severity-medium', color: '#c17d10' },
        low: { icon: 'severity-low', color: '#d99530' },
        info: { icon: 'severity-info', color: '#428fdc' },
        unknown: { icon: 'severity-unknown', color: '#89888d' },
    }
    var modal = document.getElementById("myModal")

    function openDialog (data) {
        console.log("click", data)
        document.body.style = "position:fixed;height: 100%;overflow: hidden;"
        modal.setAttribute ("class", "modal open")
        // 填充漏洞详情
        document.getElementById("myModalLabel").innerHTML = data.title || '漏洞详情'
        var modalBody = document.getElementById("myModalBody")
        var html = `
        <section class="description-section">
            <span class="head">说明</span> ${data.description}
            <p><b>状态：</b> <span class="detected-icon">DETECTED</span></p>
            <p><b>严重性：</b>
              ${data.severity ? `<span class="icon" style="color:${data.severity.color}"><svg role="img"><use xlink:href="#${data.severity.icon}"></svg></span>` : '' }
              ${data.serverityText}</p>
            <p><b>工具：</b>${data.tool}</p>
            <p><b>扫描器：</b>${data.scanner}</p>
        </section>
        <section>
            <span class="head">位置</span>
            ${data.image
            ? `<p><b>镜像：</b>${data.image}</p><p><b>命名空间：</b>${data.namespace}</p>`
            : `<p><b>文件：</b>${data.position}</p>`}
        </section>
        `
        if (data.links && data.links.length >0) {
            html += '<section class="links-section"><span class="head">链接</span>'
            data.links.forEach(function (link) {
                html += `<li><a href=${link} target="_blank" rel="nofollow noreferrer noopener">${link}</a></li>`
            })
            html += '</section>'
        }
        if (data.identifier) {
            html += `
            <section>
                <span class="head">标识</span>${data.identifier}
            </section>
            `
        }
        if (data.evidence) {
            html += `
            <section>
                <span class="head">证据</span>
                <p><b style="margin-right:8px;">${data.evidence}</b>${data.evidenceValue}</p>
            </section>
            `
        }
        if (data.solution) {
            html += `
            <section class="solution-section">
                <span style="width:16px;height:16px;margin-right:8px;">
                  <svg width="100%" height="100%">
                    <path xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd" d="M4.17 8.533C2.21 5.5 4.388 1.5 8 1.5s5.79 4 3.83 7.033L9.592 12H6.408L4.17 8.533zM5 12.584L2.91 9.347C.305 5.315 3.2 0 8 0s7.694 5.315 5.09 9.347L11 12.584V14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-1.416zm1.5.916v.5a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-.5h-3z"/>
                  </svg>
                </span>
                <span><strong>解决方案：</strong></span>
                <span>${data.solution}</span>
            </section>
            `
        }


        modalBody.innerHTML = html
    }

    function closeDialog () {
        modal.setAttribute ("class", "modal")
        document.body.style = ""
    }

    modal.addEventListener("click", function (e) {
        if (e.target === e.currentTarget) {
            closeDialog()
        }
    })


    const rows = document.querySelectorAll("#tbody tr")
    rows && rows.forEach((r) => {
        var titleEl = r.querySelectorAll("td .vuln-title-link")
        var scanTypeEl = r.querySelectorAll("td .scan-type")
        if (titleEl && titleEl.length > 0) {
            titleEl[0].addEventListener("click", function (e) {
                var tds = r.querySelectorAll("td")
                var data = {
                    title: titleEl[0].innerText,
                    detectTime: tds[0].innerHTML,
                    severity: r.getAttribute("data-severity") && severityOptions[r.getAttribute("data-severity")],
                    serverityText: tds[1].innerHTML,
                    tool: scanTypeEl && scanTypeEl.length ? scanTypeEl[0].innerHTML : '',
                    identifier: tds[3].innerHTML,
                    scanner: r.getAttribute("data-scanner") || '',
                    image: r.getAttribute("data-image") || '',
                    namespace: r.getAttribute("data-namespace") || '',
                    description: r.getAttribute("data-description") || '',
                    position: r.getAttribute("data-position") || '',
                    links: JSON.parse(r.getAttribute("data-links") || '[]'),
                    evidence: r.getAttribute("data-evidence") || '',
                    evidenceValue: r.getAttribute("data-evidence-value") || '',
                    solution: r.getAttribute("data-solution") || ''
                }
                openDialog(data)
            })
        }
    })
</script>


</body></html>