#!/usr/bin/env node

/**
 * 本地开发环境SSL证书生成脚本
 * 用于生成本地HTTPS开发服务器所需的SSL证书
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const sslDir = path.join(process.cwd(), 'ssl');
const keyFile = path.join(sslDir, 'private.key');
const certFile = path.join(sslDir, 'certificate.crt');

console.log('🔐 生成本地开发环境SSL证书...\n');

// 确保ssl目录存在
if (!fs.existsSync(sslDir)) {
  fs.mkdirSync(sslDir, { recursive: true });
  console.log('✅ 创建ssl目录');
}

// 检查是否已存在证书文件
if (fs.existsSync(keyFile) && fs.existsSync(certFile)) {
  console.log('⚠️  SSL证书文件已存在');
  console.log(`   私钥: ${keyFile}`);
  console.log(`   证书: ${certFile}`);
  
  const answer = require('readline-sync').question('\n是否要重新生成证书？(y/N): ');
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log('🚫 取消生成证书');
    process.exit(0);
  }
}

try {
  // 检查openssl是否可用
  try {
    execSync('openssl version', { stdio: 'ignore' });
  } catch (error) {
    console.error('❌ 错误: 未找到openssl命令');
    console.error('   请安装OpenSSL或使用以下替代方案:');
    console.error('   1. Windows: 下载并安装 OpenSSL for Windows');
    console.error('   2. macOS: brew install openssl');
    console.error('   3. Linux: sudo apt-get install openssl 或 sudo yum install openssl');
    console.error('\n   或者使用在线SSL证书生成工具生成证书后放入ssl目录');
    process.exit(1);
  }

  // 生成私钥
  console.log('🔑 生成私钥...');
  execSync(`openssl genrsa -out "${keyFile}" 2048`, { stdio: 'inherit' });
  
  // 生成证书
  console.log('📜 生成自签名证书...');
  const certCommand = `openssl req -new -x509 -key "${keyFile}" -out "${certFile}" -days 365 -subj "/C=CN/ST=Local/L=Local/O=Development/OU=Development/CN=localhost"`;
  execSync(certCommand, { stdio: 'inherit' });
  
  console.log('\n✅ SSL证书生成成功!');
  console.log(`   私钥文件: ${keyFile}`);
  console.log(`   证书文件: ${certFile}`);
  
  console.log('\n📋 使用说明:');
  console.log('   1. 这些证书仅用于本地开发环境');
  console.log('   2. 浏览器可能会显示"不安全"警告，这是正常的');
  console.log('   3. 在浏览器中点击"高级" -> "继续访问"即可');
  console.log('   4. 生产环境请使用正式的SSL证书');
  
  console.log('\n⚠️  安全提醒:');
  console.log('   - 这些文件已被添加到.gitignore中，不会被提交到代码库');
  console.log('   - 请勿将私钥文件分享给他人');
  console.log('   - 定期更新证书（建议每年更新一次）');

} catch (error) {
  console.error('❌ 生成SSL证书时出错:', error.message);
  console.error('\n💡 替代方案:');
  console.error('   1. 使用在线SSL证书生成工具');
  console.error('   2. 在manifest.json中将"https": true改为"https": false');
  console.error('   3. 使用mkcert工具: https://github.com/FiloSottile/mkcert');
  process.exit(1);
}
